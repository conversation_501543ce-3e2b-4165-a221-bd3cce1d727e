# ORACLE_DSN = "tg/tg202312@127.0.0.1:1521/orclpdb.docker.internal"
ORACLE_DSN = "sim_steelprocess/sim_steelprocess@10.220.200.40:1521/CISRI_POI_DB"

CRANE_SPEED = 600  # 0.6 m/s

CRANE_WORKING_INTERVALS = {
    "SPA23": {
        "CRA232": [197429, 392094 + 1],  # [A052, A030]
        "CRA231": [261276, 487648 + 1],  # [B034, SQC]
    },

    "SPA24": {
        "CRA242": [196063, 391511 + 1],  # [A052, A030]
        "CRA241": [259972, 487264 + 1],  # [B034, SQC]
    },

    "SPA25": {
        "CRA253": [199770, 313218 + 1],  # [A073, A047]
        "CRA252": [262150, 416053 + 1],  # [A056, A022]
        "CRA251": [342508, 487562 + 1],  # [A037, <PERSON>Q<PERSON>]
    },

    "SPA26": {
        "CRA262": [201674, 416601 + 1],  # [A075, A022]
        "CRA261": [268636, 487648 + 1],  # [A056, SQC]
    },
}
