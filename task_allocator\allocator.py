from copy import deepcopy

from common.base import SlabYardModel
from task_allocator.utils import utils


class TaskAllocator(SlabYardModel):
    def __init__(self):
        super().__init__()

    def assign_task(self, unassigned_task):
        """
        Assign a task to the most suitable crane based on working intervals and current load.

        Args:
            unassigned_task: Task object that needs crane assignment

        Returns:
            Task object with assigned crane
        """
        assigned_task = deepcopy(unassigned_task)
        available_cranes = self.spans[unassigned_task.span]
        task_position_interval = [
            min(unassigned_task.start_position, unassigned_task.end_position),
            max(unassigned_task.start_position, unassigned_task.end_position)
        ]

        if unassigned_task.span != 'SPA25':
            # Handle spans with 2 cranes
            crane_1, crane_2 = available_cranes[0], available_cranes[1]
            overlapping_work_intervals = utils.find_intersection(
                crane_1.working_intervals, crane_2.working_intervals)

            if utils.is_interval_inside(task_position_interval, overlapping_work_intervals):
                # Task is in overlapping area - assign to crane with fewer tasks
                if crane_1.task_num <= crane_2.task_num:
                    crane_1.task_num += 1
                    assigned_task.crane_no = crane_1.crane_no
                else:
                    crane_2.task_num += 1
                    assigned_task.crane_no = crane_2.crane_no
            else:
                # Task is not in overlapping area - assign to crane with better coverage
                crane_1_coverage_degree = utils.get_coincidence_degree(
                    task_position_interval, crane_1.working_intervals)
                crane_2_coverage_degree = utils.get_coincidence_degree(
                    task_position_interval, crane_2.working_intervals)

                if crane_1_coverage_degree >= crane_2_coverage_degree:
                    crane_1.task_num += 1
                    assigned_task.crane_no = crane_1.crane_no
                else:
                    crane_2.task_num += 1
                    assigned_task.crane_no = crane_2.crane_no
        else:
            # Handle SPA25 span with 3 cranes
            crane_1, crane_2, crane_3 = available_cranes[0], available_cranes[1], available_cranes[2]

            # Find overlapping intervals between adjacent cranes
            overlap_intervals_1_2 = utils.find_intersection(
                crane_1.working_intervals, crane_2.working_intervals)
            overlap_intervals_2_3 = utils.find_intersection(
                crane_2.working_intervals, crane_3.working_intervals)

            if utils.is_interval_inside(task_position_interval, overlap_intervals_1_2):
                # Task is in overlap between crane 1 and 2
                if crane_1.task_num <= crane_2.task_num:
                    crane_1.task_num += 1
                    assigned_task.crane_no = crane_1.crane_no
                else:
                    crane_2.task_num += 1
                    assigned_task.crane_no = crane_2.crane_no
            elif utils.is_interval_inside(task_position_interval, overlap_intervals_2_3):
                # Task is in overlap between crane 2 and 3
                if crane_2.task_num <= crane_3.task_num:
                    crane_2.task_num += 1
                    assigned_task.crane_no = crane_2.crane_no
                else:
                    crane_3.task_num += 1
                    assigned_task.crane_no = crane_3.crane_no
            else:
                # Task is not in any overlap - assign to crane with best coverage
                crane_1_coverage = utils.get_coincidence_degree(
                    task_position_interval, crane_1.working_intervals)
                crane_2_coverage = utils.get_coincidence_degree(
                    task_position_interval, crane_2.working_intervals)
                crane_3_coverage = utils.get_coincidence_degree(
                    task_position_interval, crane_3.working_intervals)

                if crane_1_coverage >= crane_2_coverage and crane_1_coverage >= crane_3_coverage:
                    crane_1.task_num += 1
                    assigned_task.crane_no = crane_1.crane_no
                elif crane_2_coverage >= crane_1_coverage and crane_2_coverage >= crane_3_coverage:
                    crane_2.task_num += 1
                    assigned_task.crane_no = crane_2.crane_no
                else:
                    crane_3.task_num += 1
                    assigned_task.crane_no = crane_3.crane_no

        return assigned_task
