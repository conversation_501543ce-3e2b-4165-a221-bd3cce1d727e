import json
import logging
import traceback

import simulation.config
from common import utils
from common.task import Task
from config.settings import get_settings
from data import api
from data.lifting_task_generator import LiftingTaskGenerator
from simulation.sim_model import SimulationModel
from simulation.utils import coordinate_utils
from task_allocator import allocator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def exception_handler(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception:
            return str(traceback.format_exc())

    return wrapper


@exception_handler
def run():
    """
    Main execution function for crane scheduling simulation.

    Returns:
        list: Simulation results or error message
    """
    # Get data from external APIs
    api_data = api.get_data()

    # Initialize components
    task_allocator = allocator.TaskAllocator()
    simulation_model = SimulationModel()
    lifting_task_generator = LiftingTaskGenerator()

    # Generate lifting tasks from API data
    lifting_task_generator.create_lifting_tasks(
        api_data["roll_plan"],
        api_data["cut_plan"],
        api_data["restore_plan"]
    )
    generated_tasks = lifting_task_generator.tasks

    # Convert tasks to simulation format and assign cranes
    simulation_tasks = []
    for task_index, task_data in enumerate(generated_tasks):
        # Use slab_no if available, otherwise use virtual_slab_no
        effective_slab_no = task_data["slab_no"] if task_data["slab_no"] else task_data["virtual_slab_no"]

        # Create task object with proper coordinates
        task_object = Task(
            item_id=task_index,
            start_position=utils.get_x_coordinate(task_data["from_pile_no"]),
            end_position=utils.get_x_coordinate(task_data["to_pile_no"]),
            from_pile_no=task_data["from_pile_no"],
            to_pile_no=task_data["to_pile_no"],
            start_time=task_data["plan_start_time"],
            roll_no=task_data["roll_no"],
            end_time=task_data["plan_end_time"],
            slab_no=effective_slab_no,
            span=task_data["span"],
            roll_seq=task_data["roll_seq"],
            priority=task_data["property"],
        )

        # Convert to simulation coordinates and assign crane
        simulation_task = coordinate_utils.map_task_to_sim(task_object)
        assigned_task = task_allocator.assign_task(simulation_task)
        simulation_tasks.append(assigned_task)
    # Set up simulation with assigned tasks
    simulation_model.tasks = simulation_tasks
    try:
        simulation.config.run_time = simulation_tasks[0].start_time
    except IndexError:
        raise ValueError("当前轧程没有可执行的任务！")

    # Run simulation loop
    while True:
        simulation_model.execute()
        simulation_model.run_simulation()

        if simulation_model.is_all_tasks_finished():
            simulation_results = simulation_model.get_all_results()
            formatted_results = [utils.instance_to_dict(result_obj) for result_obj in simulation_results]
            break

    return formatted_results


def main():
    """
    Main entry point for the crane scheduling application.

    Returns:
        str: JSON string containing results or error information
    """
    execution_result = {"error_message": "", "items": []}

    try:
        simulation_output = run()
        if isinstance(simulation_output, str):
            # Error case - run() returned error string
            execution_result["error_message"] = simulation_output
        else:
            # Success case - run() returned results list
            execution_result["items"] = simulation_output
    except Exception as e:
        execution_result["error_message"] = str(e)

    return json.dumps(execution_result)


if __name__ == "__main__":
    """Main execution block."""
    try:
        settings = get_settings()
        result_json = main()

        # Ensure temp directory exists
        import os
        os.makedirs(os.path.dirname(settings.app.result_file), exist_ok=True)

        # Write results to configured file path
        with open(settings.app.result_file, "w", encoding='utf-8') as result_file:
            result_file.write(result_json)

        logger.info(f"Results written to {settings.app.result_file}")

    except Exception as e:
        logger.error(f"Application failed: {e}")
        raise
