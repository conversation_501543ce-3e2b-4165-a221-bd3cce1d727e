from concurrent.futures import ThreadPoolExecutor

import requests

from data import config


def get_roll_plan_and_restore_slab_list():
    url = "http://10.220.23.28:44357/api/roll/roll-plan/not-complete-roll-plan"
    res = requests.get(url).json()
    roll_plan = sorted(
        [item for item in res if item["status"] < 20],
        key=lambda x: x["planStart"]
    )
    current_plan = None
    for roll_group in roll_plan:
        for item in roll_group['rollPlanItems']:
            if item['status'] < 1:
                current_plan = roll_group
                break
        if current_plan:
            break
    try:
        config.end_time = current_plan['rollPlanItems'][-1]['planInFurnaceTime']
    except TypeError:
        raise ValueError("当前没有未完成的入炉计划")
    restore_slab_list = [
        item["slabNo"]
        for item in current_plan["rollPlanItems"] if item["needRestorage"]
    ]
    return current_plan, restore_slab_list


# 获取切割计划
def get_cut_plan():
    url = "http://10.220.23.28:44356/api/steel/cutting-plan"
    params = {
        "startTime": config.start_time,
        "endTime": config.end_time,
        "isPage": False,
    }
    res = requests.get(url, params=params).json()
    cut_plan = sorted(
        res['items'],
        key=lambda x: x['endTime']
    )
    # 排除其中casterNo为'CC603'的数据
    cut_plan = [item for item in cut_plan if item['casterNo'] != 'CC603']
    return cut_plan


# 获取库存板坯信息
def get_stock_slabs():
    url = "http://10.220.23.28:44357/api/roll/slab-pile-definition/stock-slabs"
    stock_info = requests.get(url).json()
    return stock_info


# 获取天车信息
def get_crane_info():
    url = "http://10.220.23.28:44360/api/crane/crane-state"
    crane_no_list = ["CRA231", "CRA232", "CRA241", "CRA242", "CRA251", "CRA252", "CRA253", "CRA261", "CRA262", ]
    params = {
        "craneNoList": crane_no_list,
    }
    crane_info = requests.get(url, params=params).json()
    # slab_no = [
    #     obj for item in crane_info['items'] if item['objects']
    #     for obj in (item['objects'].split(";") if ";" in item['objects'] else [item['objects']])
    # ]

    return crane_info


def get_restore_plan_by_slab_no(slab_no):
    url = "http://10.220.23.28:44372/api/roll/slab-restorage-plan"
    params = {
        "isPage": False,
        "targetSlabNo": slab_no,
        "statusList": [-1, 0, 1],
    }
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjUyMkEyN0MyMjgyOEFBQ0VGNTY4NUZGNUVEQkFFMzJERTE5ODZDRjNSUzI1NiIsInR5cCI6ImF0K2p3dCIsIng1dCI6IlVpb253aWdvcXM3MWFGXzE3YnJqTGVHWWJQTSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B2EdfVVDputB9TJB4SksHB9VPf-B9e40dBnFl3nHuqViDObDOVPkwA1E6UMWURYF8BY0o55Wpxu7qJ1O9ppGzB34O1To9V7lmBV2K7_AdcLLzmx1y8vTAPNmFZ65V0qmBwGiTWiNVvXp-CXGBDd4s4B2cGtF2bbU1xuChzmyaVI8KolNVr0o56gCM6mD5QdkYsKAcJHHTZr3gEs6Uxu9zp96_tE3jZfX0k_kQVAjPv3HZFM-PnDv6xaaBktYP9eWmX37CwJp1Qg6hLjIJDlfrYSASDSxeZsp1poEFmvcO0cGY5DQ4vVNzMNX-9jPbUrNPHrcejzuoORxLby7S_EoCw",
    }
    restore_plan = requests.get(url, params=params, headers=headers).json()
    return restore_plan


def get_restore_plan_by_slab_no_list(restore_slab_list):
    restore_plan = []
    for slab_no in restore_slab_list:
        restore_plan.append(get_restore_plan_by_slab_no(slab_no))

    return restore_plan


def get_data():
    roll_plan, restore_list = get_roll_plan_and_restore_slab_list()
    with ThreadPoolExecutor(max_workers=5) as executor:
        # 倒垛计划
        restore = executor.submit(get_restore_plan_by_slab_no_list, restore_list)
        # 切割计划
        cut_plan = executor.submit(get_cut_plan)
        # 库存板坯信息
        stock_slabs = executor.submit(get_stock_slabs)
        # 天车信息
        crane_info = executor.submit(get_crane_info)

        data = {
            "roll_plan": roll_plan,
            "restore_plan": restore.result(),
            "cut_plan": cut_plan.result(),
            "stock_info": stock_slabs.result(),
            "crane_info": crane_info.result(),
        }
    return data
