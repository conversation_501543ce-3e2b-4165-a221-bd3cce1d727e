# Legacy simulation configuration - DEPRECATED
# This file is being refactored. Please use the new configuration system.

import warnings
from config.settings import get_settings

warnings.warn(
    "Direct import from simulation.config is deprecated. Use 'from config.settings import get_settings' instead.",
    DeprecationWarning,
    stacklevel=2
)

# Backward compatibility - delegate to new configuration system
_settings = get_settings()

# 坐标映射偏移量
COORDINATE_OFFSET = _settings.simulation.coordinate_offset
# 坐标映射比例
COORDINATE_SCALE = _settings.simulation.coordinate_scale

# 实际安全距离
REAL_SAFE_DISTANCE = _settings.simulation.real_safe_distance
# 仿真安全距离
SIMULATION_SAFE_DISTANCE = REAL_SAFE_DISTANCE / COORDINATE_SCALE
# 仿真行车速度
SIMULATION_CRANE_SPEED = _settings.crane.speed / COORDINATE_SCALE

# Application state variable
run_time = 0

# 天车编号与天车名称的映射
CRANE_NO_TO_NAME = _settings.simulation.crane_name_mapping