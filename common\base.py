import config


class Crane:
    def __init__(self, crane_no, span, task_num=0):
        self.crane_no = crane_no
        self.task_num = task_num
        self.span = span
        self.working_intervals = config.CRANE_WORKING_INTERVALS[span][crane_no]

    def __repr__(self):
        return (f"crane_no: {self.crane_no}, span: {self.span}, "
                f"task_num: {self.task_num}, working_intervals: {self.working_intervals}")


class SlabYardModel:
    def __init__(self):
        self.spans = {span: [] for span in list(config.CRANE_WORKING_INTERVALS.keys())}
        for span in self.spans.keys():
            for crane_no in config.CRANE_WORKING_INTERVALS[span].keys():
                self.spans[span].append(Crane(crane_no, span))

    def __repr__(self):
        return str(self.spans)


if __name__ == '__main__':
    slab_yard_model = SlabYardModel()
    print(slab_yard_model)
    print(slab_yard_model.spans['SPA25'][0].working_intervals)
    for item in slab_yard_model.spans['SPA25']:
        print(item.crane_no, item.span, item.task_num, item.working_intervals)
