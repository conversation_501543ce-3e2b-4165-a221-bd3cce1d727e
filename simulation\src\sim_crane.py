from enum import Enum

from common.base import Crane
from common.task import Task
from .. import config


class Direction(Enum):
    LEFT = "左"
    RIGHT = "右"
    STOP = "停"


class LoadStatus(Enum):
    EMPTY = "空载"
    LOADED = "负载"


class MainSlaveStatus(Enum):
    MAIN_CRANE = "主吊车"
    SECONDARY_CRANE = "从天车"


class LiftingStatus(Enum):
    LIFTING = "起吊"
    LOWERING = "落吊"
    NOT_LIFTING = "非起落吊"


class State:
    def __init__(
        self,
        load_status=LoadStatus.EMPTY,
        main_slave=MainSlaveStatus.MAIN_CRANE,
        lifting_status=LiftingStatus.NOT_LIFTING,
    ):
        self.load_status = load_status
        self.main_slave = main_slave
        self.lifting_status = lifting_status

    def __repr__(self):
        return f"State({self.load_status}, {self.main_slave}, {self.lifting_status})"


class SimCrane(Crane):
    def __init__(
        self,
        position: float,
        speed: float = config.SIMULATION_CRANE_SPEED,
        state: State | None = None,
        direction: Direction = Direction.STOP,
        span: str | None = None,
        task: Task | None = None,
        secondary_task: Task | None = None,
        crane_no: str | None = None,
    ):
        super().__init__(crane_no, span)
        self.position = position
        self.speed = speed
        self.direction = direction
        self.state = state if state else State()
        self.lifting_time = 60
        self.results = []
        self.task = task
        self.secondary_task = secondary_task

    def __repr__(self):
        return (
            f"crane_no: {self.crane_no}, span: {self.span}, task_num: {self.task_num},"
            f"position: {self.position}, speed: {self.speed}, direction: {self.direction}, "
            f"state: {self.state}, lifting_time: {self.lifting_time}, results: {self.results}, "
            f"task: {self.task}, secondary_task: {self.secondary_task}"
        )

    def move(self):
        if self.direction == Direction.LEFT:
            self.position -= self.speed
        elif self.direction == Direction.RIGHT:
            self.position += self.speed
        elif self.direction == Direction.STOP:
            pass
        else:
            raise ValueError("Direction is not valid!")

    def change_to_slave(self):
        self.state.main_slave = MainSlaveStatus.SECONDARY_CRANE

    def change_to_main(self):
        self.state.main_slave = MainSlaveStatus.MAIN_CRANE

    # 调运任务执行函数
    def transport_slabs(self):
        if self.task:
            task_position = (
                self.task.start_position
                if self.state.load_status == LoadStatus.EMPTY or self.state.lifting_status == LiftingStatus.LIFTING
                else self.task.end_position
            )
            if abs(self.position - task_position) > self.speed:
                self.direction = Direction.LEFT if self.position > task_position else Direction.RIGHT
            else:
                self.direction = Direction.STOP
                if (
                    self.state.load_status == LoadStatus.LOADED
                    and self.state.lifting_status == LiftingStatus.LIFTING
                    and self.lifting_time == 59
                ):
                    self.task.actual_start_time = config.run_time
                    if self.secondary_task:
                        self.secondary_task.actual_start_time = self.task.actual_start_time
                if self.state.load_status == LoadStatus.EMPTY or self.state.lifting_status == LiftingStatus.LIFTING:
                    self.state.load_status = LoadStatus.LOADED
                    self.state.lifting_status = LiftingStatus.LIFTING
                    if self.lifting_time > 0:
                        self.lifting_time -= 1
                    else:
                        self.state.lifting_status = LiftingStatus.NOT_LIFTING
                        self.lifting_time = 60
                else:
                    self.state.lifting_status = LiftingStatus.LOWERING
                    if self.lifting_time > 0:
                        self.lifting_time -= 1
                    else:
                        self.state.load_status = LoadStatus.EMPTY
                        self.state.lifting_status = LiftingStatus.NOT_LIFTING
                        self.lifting_time = 60
                        self.task.actual_end_time = config.run_time
                        # 计算时间差值
                        self.task.diff = self.task.actual_start_time - self.task.start_time
                        # 插值结果转化为分钟，使用float类型
                        self.task.diff = self.task.diff.total_seconds() / 60
                        self.task.crane_name = config.CRANE_NO_TO_NAME[self.crane_no]
                        self.results.append(self.task)
                        if self.secondary_task:
                            self.secondary_task.actual_end_time = self.task.actual_end_time
                            self.secondary_task.diff = (
                                self.secondary_task.actual_start_time - self.secondary_task.start_time
                            )
                            self.secondary_task.diff = self.secondary_task.diff.total_seconds() / 60
                            self.secondary_task.crane_name = config.CRANE_NO_TO_NAME[self.crane_no]
                            self.results.append(self.secondary_task)
                        self.task = None
                        self.secondary_task = None
            self.move()

    # 避让任务执行函数
    def avoid_task(self):
        self.move()

    def run(self):
        if self.state.main_slave == MainSlaveStatus.MAIN_CRANE:
            # 执行调运任务
            self.transport_slabs()
        elif self.state.main_slave == MainSlaveStatus.SECONDARY_CRANE:
            # 执行避让任务
            self.avoid_task()
        else:
            raise ValueError("MainSlaveStatus is not valid!")

    def assign_task(self, task, secondary_task=None):
        self.task = task
        self.secondary_task = secondary_task

    # 天车是否有任务
    def is_free(self):
        return self.task is None
