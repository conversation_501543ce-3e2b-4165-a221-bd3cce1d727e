# Crane Scheduling System Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=False
LOG_LEVEL=INFO

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Oracle Database Connection
ORACLE_DSN=sim_steelprocess/sim_steelprocess@your-oracle-host:1521/your-database

# MySQL Database Configuration
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DATABASE=your-mysql-database

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Base API URL
API_BASE_URL=http://your-api-host

# Service-specific URLs
ROLL_SERVICE_URL=http://your-api-host:44357
STEEL_SERVICE_URL=http://your-api-host:44356
CRANE_SERVICE_URL=http://your-api-host:44360

# Authentication
API_AUTH_TOKEN=your-bearer-token-here

# API Settings
API_TIMEOUT=30

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
# File paths
TEMP_DIR=./temp
RESULT_FILE=./temp/result.json

# =============================================================================
# NOTES
# =============================================================================
# - Never commit the actual .env file to version control
# - Keep sensitive information like passwords and tokens secure
# - Use different values for development, testing, and production environments
# - Boolean values: use True/False, 1/0, yes/no, on/off
