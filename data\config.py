# Legacy data configuration - DEPRECATED
# This file is being refactored. Please use the new configuration system.

import datetime
import warnings
from config.settings import get_settings

warnings.warn(
    "Direct import from data.config is deprecated. Use 'from config.settings import get_settings' instead.",
    DeprecationWarning,
    stacklevel=2
)

# Application state variables
start_time = datetime.datetime.now()
end_time = None

# Backward compatibility - delegate to new configuration system
_settings = get_settings()

MYSQL_DSN = {
    "user": _settings.database.mysql_user,
    "password": _settings.database.mysql_password,
    "host": _settings.database.mysql_host,
    "port": _settings.database.mysql_port,
    "database": _settings.database.mysql_database
}
