"""
Centralized configuration management for crane scheduling system.

This module provides a unified configuration system that loads settings from:
1. Environment variables
2. .env files
3. Default values

Usage:
    from config.settings import get_settings
    
    settings = get_settings()
    print(settings.database.oracle_dsn)
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from pathlib import Path

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    oracle_dsn: str = ""
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_user: str = ""
    mysql_password: str = ""
    mysql_database: str = ""


@dataclass
class APIConfig:
    """API configuration settings."""
    base_url: str = "http://localhost"
    roll_service_url: str = ""
    steel_service_url: str = ""
    crane_service_url: str = ""
    auth_token: str = ""
    timeout: int = 30


@dataclass
class CraneConfig:
    """Crane system configuration settings."""
    speed: int = 600  # 0.6 m/s
    working_intervals: Dict[str, Dict[str, List[int]]] = field(default_factory=lambda: {
        "SPA23": {
            "CRA232": [197429, 392095],  # [A052, A030]
            "CRA231": [261276, 487649],  # [B034, SQC]
        },
        "SPA24": {
            "CRA242": [196063, 391512],  # [A052, A030]
            "CRA241": [259972, 487265],  # [B034, SQC]
        },
        "SPA25": {
            "CRA253": [199770, 313219],  # [A073, A047]
            "CRA252": [262150, 416054],  # [A056, A022]
            "CRA251": [342508, 487563],  # [A037, SQC]
        },
        "SPA26": {
            "CRA262": [201674, 416602],  # [A075, A022]
            "CRA261": [268636, 487649],  # [A056, SQC]
        },
    })


@dataclass
class SimulationConfig:
    """Simulation configuration settings."""
    coordinate_offset: int = 196023
    coordinate_scale: int = 200
    real_safe_distance: int = 6000
    crane_name_mapping: Dict[str, str] = field(default_factory=lambda: {
        'CRA231': '46#',
        'CRA232': '45#',
        'CRA241': '48#',
        'CRA242': '47#',
        'CRA251': '52#',
        'CRA252': '51#',
        'CRA253': '50#',
        'CRA261': '54#',
        'CRA262': '53#',
    })


@dataclass
class AppConfig:
    """Application configuration settings."""
    environment: str = "development"
    debug: bool = False
    log_level: str = "INFO"
    temp_dir: str = "./temp"
    result_file: str = "./temp/result.json"


@dataclass
class Settings:
    """Main settings container."""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    api: APIConfig = field(default_factory=APIConfig)
    crane: CraneConfig = field(default_factory=CraneConfig)
    simulation: SimulationConfig = field(default_factory=SimulationConfig)
    app: AppConfig = field(default_factory=AppConfig)


def load_env_file(env_file_path: Optional[str] = None) -> None:
    """Load environment variables from .env file if available."""
    if not DOTENV_AVAILABLE:
        return
    
    if env_file_path is None:
        # Look for .env file in project root
        project_root = Path(__file__).parent.parent
        env_file_path = project_root / ".env"
    
    if Path(env_file_path).exists():
        load_dotenv(env_file_path)


def get_env_var(key: str, default: str = "", var_type: type = str):
    """Get environment variable with type conversion."""
    value = os.getenv(key, default)
    
    if var_type == bool:
        return value.lower() in ('true', '1', 'yes', 'on') if isinstance(value, str) else bool(value)
    elif var_type == int:
        try:
            return int(value)
        except (ValueError, TypeError):
            return int(default) if default else 0
    elif var_type == float:
        try:
            return float(value)
        except (ValueError, TypeError):
            return float(default) if default else 0.0
    
    return value


def create_settings_from_env() -> Settings:
    """Create settings instance from environment variables."""
    # Load .env file
    load_env_file()
    
    # Database configuration
    database = DatabaseConfig(
        oracle_dsn=get_env_var("ORACLE_DSN", "sim_steelprocess/sim_steelprocess@*************:1521/CISRI_POI_DB"),
        mysql_host=get_env_var("MYSQL_HOST", "************"),
        mysql_port=get_env_var("MYSQL_PORT", "3306", int),
        mysql_user=get_env_var("MYSQL_USER", "root"),
        mysql_password=get_env_var("MYSQL_PASSWORD", "123456"),
        mysql_database=get_env_var("MYSQL_DATABASE", "expert_pika"),
    )
    
    # API configuration
    api = APIConfig(
        base_url=get_env_var("API_BASE_URL", "http://************"),
        roll_service_url=get_env_var("ROLL_SERVICE_URL", "http://************:44357"),
        steel_service_url=get_env_var("STEEL_SERVICE_URL", "http://************:44356"),
        crane_service_url=get_env_var("CRANE_SERVICE_URL", "http://************:44360"),
        auth_token=get_env_var("API_AUTH_TOKEN", ""),
        timeout=get_env_var("API_TIMEOUT", "30", int),
    )
    
    # Application configuration
    app = AppConfig(
        environment=get_env_var("ENVIRONMENT", "development"),
        debug=get_env_var("DEBUG", "False", bool),
        log_level=get_env_var("LOG_LEVEL", "INFO"),
        temp_dir=get_env_var("TEMP_DIR", "./temp"),
        result_file=get_env_var("RESULT_FILE", "./temp/result.json"),
    )
    
    return Settings(
        database=database,
        api=api,
        crane=CraneConfig(),  # Use defaults for crane config
        simulation=SimulationConfig(),  # Use defaults for simulation config
        app=app,
    )


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = create_settings_from_env()
    return _settings


def reload_settings() -> Settings:
    """Reload settings from environment variables."""
    global _settings
    _settings = create_settings_from_env()
    return _settings
