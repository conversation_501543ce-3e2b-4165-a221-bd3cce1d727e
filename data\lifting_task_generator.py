import datetime

import pymysql

from data import config
from data.utils import utils
from simulation import config as simu_config


class LiftingTaskGenerator:
    def __init__(self):
        self.conn = pymysql.connect(**config.MYSQL_DSN)
        self.cursor = self.conn.cursor()
        self.tasks = []  # property: 0-出库，1-入库, 2-倒垛，3-其他
        self.roll_plan = []

    def __del__(self):
        self.cursor.close()
        self.conn.close()

    def create_outbound_tasks(self):
        for item in self.roll_plan:
            # 创建出库吊运记录
            if item['status'] < 1 and item['pileNo']:
                span = item['pileNo'].split('_')[0]
                from_pile_no, to_pile_no = item['pileNo'], span + "_SRSGD"
                plan_in_furnace_time = datetime.datetime.strptime(item['planInFurnaceTime'], '%Y-%m-%d %H:%M:%S')
                plan_end_time = plan_in_furnace_time - datetime.timedelta(seconds=180)
                trans_distance = utils.compute_distance_between_piles(from_pile_no, to_pile_no)
                # TODO: 运输时间包含起落吊时间，设置为 120 s
                trans_time = trans_distance / simu_config.SIMULATION_CRANE_SPEED + 120
                plan_start_time = plan_end_time - datetime.timedelta(seconds=trans_time)
                record = {
                    'roll_no': item['rollNo'],
                    'roll_seq': item['rollSeq'],
                    'slab_no': item['slabNo'],
                    'virtual_slab_no': item['virtualSlabNo'],
                    'from_pile_no': from_pile_no,
                    'from_layer': item['layer'],
                    'to_pile_no': to_pile_no,
                    'plan_start_time': plan_start_time,
                    'plan_end_time': plan_end_time,
                    'span': span,
                    'property': 0
                }
                self.tasks.append(record)

    def create_inbound_tasks(self, cut_plan):
        query_mysql = "select produceTime, pileNo from t_planslab_inbound_pile_recom where virtualSlabNo = %s"
        for item in cut_plan:
            # 创建入库吊运记录
            self.cursor.execute(query_mysql, (item['virtualSlabNo'],))
            result = self.cursor.fetchone()
            if result and result[1]:
                produce_time, to_pile_no = result
                produce_time = datetime.datetime.strptime(produce_time, '%Y-%m-%d %H:%M:%S')
                span = to_pile_no.split('_')[0]
                from_pile_no = span + "_SRSGD"
                plan_start_time = produce_time + datetime.timedelta(seconds=180)
                roll_no, roll_seq = "", ""

                trans_distance = utils.compute_distance_between_piles(from_pile_no, to_pile_no)
                # TODO: 运输时间包含起落吊时间，设置为 120 s
                trans_time = trans_distance / simu_config.SIMULATION_CRANE_SPEED + 120
                plan_end_time = plan_start_time + datetime.timedelta(seconds=trans_time)

                # 在 self.roll_plan 中查询 item["virtual"] 是否存在
                res = filter(lambda query_item: query_item['virtualSlabNo'] == item['virtualSlabNo'], self.roll_plan)
                res = list(res)
                if res:
                    roll_no = res[0]['rollNo']
                    roll_seq = res[0]['rollSeq']

                record = {
                    'roll_no': roll_no,
                    'roll_seq': roll_seq,
                    'slab_no': "",
                    'virtual_slab_no': item['virtualSlabNo'],
                    'from_pile_no': from_pile_no,
                    'from_layer': 0,
                    'to_pile_no': to_pile_no,
                    'plan_start_time': plan_start_time,
                    'plan_end_time': plan_end_time,
                    'span': span,
                    'property': 1
                }
                self.tasks.append(record)
            else:
                print(f"板坯{item['virtualSlabNo']}在切割计划中，但不在入库计划中")

    def create_restore_tasks(self, restore_plan):
        processed_slabs = []
        for plan in restore_plan:
            target_slab_no = plan['items'][0]['targetSlabNo']
            res = list(filter(lambda query_item: query_item['slab_no'] == target_slab_no, self.tasks))
            plan_start_time = res[0]['plan_start_time']
            for item in plan['items']:
                plan_end_time = plan_start_time - datetime.timedelta(seconds=80)
                slab_no = item['slabNo']
                if slab_no in processed_slabs:
                    continue
                processed_slabs.append(slab_no)
                from_pile_no, from_layer = item['fromPileNo'], item['fromLayer']

                span = from_pile_no.split('_')[0]
                to_pile_no = item['toPileNo'] if item['toPileNo'] else span + "_SQC"

                trans_distance = utils.compute_distance_between_piles(from_pile_no, to_pile_no)
                trans_time = trans_distance / simu_config.SIMULATION_CRANE_SPEED + 120
                plan_start_time = plan_end_time - datetime.timedelta(seconds=trans_time)
                roll_seq = ""
                res = list(filter(lambda query_item: query_item['slabNo'] == item['slabNo'], self.roll_plan))
                if res:
                    roll_seq = res[0]['rollSeq']
                record = {
                    'roll_no': item['rollNo'],
                    'roll_seq': roll_seq,
                    'slab_no': item['slabNo'],
                    'virtual_slab_no': "",  # 无用，留空
                    'from_pile_no': from_pile_no,
                    'from_layer': from_layer,
                    'to_pile_no': to_pile_no,
                    'plan_start_time': plan_start_time,
                    'plan_end_time': plan_end_time,
                    'span': span,
                    'property': 2
                }
                self.tasks.append(record)

    def create_lifting_tasks(self, roll_plan, cut_plan, restore_plan):
        self.roll_plan = roll_plan['rollPlanItems']
        self.create_outbound_tasks()
        self.create_inbound_tasks(cut_plan)
        self.create_restore_tasks(restore_plan)
        return self.tasks
