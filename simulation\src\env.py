from typing import List

import pygame

from common.task import Task
from .sim_crane import Sim<PERSON>rane, LoadStatus

FONT_SIZE_COEFFICIENT = 0.02


class Env:
    def __init__(self, window_width: int, window_height: int) -> None:
        pygame.init()
        pygame.font.init()
        pygame.display.set_caption("天车调度系统")

        self.window_width = window_width
        self.window_height = window_height

        self.clock = pygame.time.Clock()

        self.screen = pygame.display.set_mode((self.window_width, self.window_height))

        self.font_size = int(FONT_SIZE_COEFFICIENT * self.window_height)
        self.font = pygame.font.SysFont("Microsoft YaHei", self.font_size)

        self.margin = 0.13 * self.window_height
        self.span_width = (self.window_height - 5 * self.margin) / 4
        self.crane_height = self.span_width
        self.crane_width = self.span_width * 0.618

        self.track = {
            "SPA23": (self.margin, self.margin + self.span_width),
            "SPA24": (2 * self.margin + self.span_width, 2 * self.margin + 2 * self.span_width),
            "SPA25": (3 * self.margin + 2 * self.span_width, 3 * self.margin + 3 * self.span_width),
            "SPA26": (4 * self.margin + 3 * self.span_width, 4 * self.margin + 4 * self.span_width)
        }

    @staticmethod
    def handle_event() -> None:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                exit()

    def plot_track(self) -> None:
        for span, position in self.track.items():
            pygame.draw.line(
                self.screen, "black", (0, position[0]), (self.window_width, position[0]), 3
            )
            pygame.draw.line(
                self.screen, "black", (0, position[1]), (self.window_width, position[1]), 3
            )
            self.screen.blit(
                self.font.render(span, True, "black"),
                (0, position[1] - self.font_size - 5),
            )

    def plot_crane(self, crane: SimCrane) -> None:
        if crane.state.load_status == LoadStatus.LOADED:
            attr = {"color": "blue", "width": 0, "label": "load"}
        else:
            attr = {"color": "black", "width": 3, "label": "free"}

        position = crane.position
        track_up, track_down = self.track[crane.span]

        pygame.draw.rect(
            self.screen,
            attr["color"],
            (position, track_up, self.crane_width, self.crane_height),
            width=attr["width"],
        )
        self.screen.blit(
            self.font.render(attr["label"], True, "black"),
            (position, track_up - self.font_size - 10),
        )

        self.screen.blit(
            self.font.render(crane.crane_no, True, "black"),
            (position, track_down + 10),
        )

    def plot_task(self, task: Task) -> None:
        for position, label in zip(
                [task.start_position, task.end_position],
                [f"任务{task.roll_seq}起点", f"任务{task.roll_seq}终点"],
        ):
            if position is not None:
                position = position
                track_up, track_down = self.track[task.span]
                pygame.draw.rect(
                    self.screen,
                    "blue",
                    (position, track_up, self.crane_width, self.crane_height),
                )
                self.screen.blit(
                    self.font.render(label, True, "black"),
                    (position, track_up - self.font_size - 10),
                )

    def show(self, cranes: List[SimCrane], tasks: List[Task] | None = None) -> None:
        self.screen.fill("white")
        self.plot_track()
        for crane in cranes:
            self.plot_crane(crane)
        if tasks:
            for task in tasks:
                if task:
                    self.plot_task(task)
        pygame.display.update()
        self.clock.tick(30)
