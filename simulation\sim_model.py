import datetime
import itertools

from common.base import SlabYardModel
from simulation import config
from simulation.src.sim_crane import <PERSON>m<PERSON>rane
from simulation.utils.coordinate_utils import real2sim
from simulation.utils.coordinator import coordinator
from simulation.utils.get_crane_position import get_crane_position


class SimulationModel(SlabYardModel):
    def __init__(self):
        super().__init__()
        for span in self.spans.keys():
            for i, crane in enumerate(self.spans[span]):
                position = get_crane_position(crane.crane_no)
                position = real2sim(position)
                self.spans[span][i] = SimCrane(position=position, span=span, crane_no=crane.crane_no)
        self.tasks = None
        self.max_current_task_index = 0
        self.inbound_tasks = []
        self.results = []

    def __repr__(self):
        return str(self.spans)

    def step(self, span):
        if abs(self.spans[span][0].position - self.spans[span][1].position) < config.SIMULATION_SAFE_DISTANCE:
            avoid_crane, avoid_direction = coordinator(self.spans[span][0], self.spans[span][1])
            if avoid_crane:
                # 根据slave_crane的id找到对应的天车
                avoid_crane = self.spans[span][0] \
                    if avoid_crane.crane_no == self.spans[span][0].crane_no else self.spans[span][1]
                avoid_crane.direction = avoid_direction
                avoid_crane.change_to_slave()
        self.spans[span][0].run()
        self.spans[span][1].run()
        self.spans[span][0].change_to_main()
        self.spans[span][1].change_to_main()

    def run_simulation(self):
        config.run_time += datetime.timedelta(seconds=1)
        for span in self.spans.keys():
            self.step(span)

    def get_all_cranes(self):
        return list(itertools.chain.from_iterable(self.spans.values()))

    def get_all_current_tasks(self):
        cranes = self.get_all_cranes()
        tasks = [crane.task for crane in cranes]
        return tasks

    # 根据天车号和span号找到对应的天车
    def __get_crane_by_crane_no(self, crane_no, span):
        for crane in self.spans[span]:
            if crane.crane_no == crane_no:
                return crane

    def execute(self):
        task = self.tasks[0]
        if task.priority == 1:
            # 入库任务
            self.inbound_tasks.append(task)
            self.tasks.remove(task)
        else:
            # 其他：出库、倒垛、指定
            if self.__get_crane_by_crane_no(task.crane_no, task.span).is_free():
                self.__get_crane_by_crane_no(task.crane_no, task.span).assign_task(task)
                self.tasks.remove(task)
        if self.inbound_tasks:
            task = self.inbound_tasks[0]
            if config.run_time >= task.start_time and self.__get_crane_by_crane_no(task.crane_no, task.span).is_free():
                self.__get_crane_by_crane_no(task.crane_no, task.span).assign_task(task)
                self.inbound_tasks.remove(task)

    def get_all_results(self):
        cranes = self.get_all_cranes()
        for crane in cranes:
            if crane.results:
                self.results.extend(crane.results)
                crane.results = []
        return self.results

    def is_all_tasks_finished(self):
        return len(self.tasks) == 0
