def find_intersection(interval1, interval2):
    """
    Find the intersection of two intervals
    """
    start_intersection = max(interval1[0], interval2[0])
    end_intersection = min(interval1[1], interval2[1])
    if start_intersection > end_intersection:
        return None
    else:
        return [start_intersection, end_intersection]


def is_interval_inside(interval1, interval2):
    """
    Determine if interval1 is inside interval2
    """
    if interval1[0] >= interval2[0] and interval1[1] <= interval2[1]:
        return True
    else:
        return False


def get_coincidence_degree(interval1, interval2):
    """
    Get the coincidence degree of interval1 and interval2
    """
    overlap_interval = find_intersection(interval1, interval2)
    if overlap_interval is None:
        return 0
    else:
        overlap_length = overlap_interval[1] - overlap_interval[0]
        interval1_length = interval1[1] - interval1[0]
        coincidence = overlap_length / interval1_length if interval1_length > 0 else 0
        return coincidence
