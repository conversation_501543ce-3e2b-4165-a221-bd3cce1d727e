class Task:
    def __init__(self, item_id: int, start_position: float, end_position: float, start_time: str, end_time: str,
                 from_pile_no: str, to_pile_no: str, slab_no: str, span: str, priority: int = 0,
                 crane_no: str | None = None, roll_no: str = None, roll_seq: int = None):
        self.item_id = item_id
        self.start_position = start_position
        self.end_position = end_position
        self.start_time = start_time
        self.end_time = end_time
        self.from_pile_no = from_pile_no
        self.to_pile_no = to_pile_no
        self.slab_no = slab_no
        self.span = span
        self.priority = priority
        self.crane_no = crane_no
        self.crane_name = None
        self.roll_no = roll_no
        self.roll_seq = roll_seq
        self.actual_start_time = None
        self.actual_end_time = None
        self.diff = None

    def __repr__(self):
        return (f"Task({self.item_id}, {self.start_position}, {self.end_position}, {self.start_time}, {self.end_time}, "
                f"{self.priority}, {self.span}, {self.crane_no}, {self.slab_no}, {self.roll_no}, {self.roll_seq}, "
                f"{self.actual_start_time}, {self.actual_end_time}, {self.diff})")
