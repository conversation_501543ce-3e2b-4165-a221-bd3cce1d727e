from simulation.src.sim_crane import Sim<PERSON><PERSON>, LiftingStatus, LoadStatus, Direction


def coordinator(crane1: SimCrane, crane2: SimCrane):
    slave_crane = None
    slave_direction = None
    if crane1.state.lifting_status in [LiftingStatus.LIFTING, LiftingStatus.LOWERING]:
        # 天车1起吊或者落吊状态
        if crane2.state.lifting_status in [LiftingStatus.LIFTING, LiftingStatus.LOWERING]:
            # 天车2起吊或者落吊状态,不做改变
            pass
        elif crane2.state.lifting_status == LiftingStatus.NOT_LIFTING \
                and crane2.state.load_status == LoadStatus.LOADED:
            # 天车2处于运输状态
            if crane2.direction != Direction.RIGHT:
                # 不向右运动时，停止天车2
                slave_crane, slave_direction = crane2, Direction.STOP
        else:
            #
            if crane2.task:
                # 天车2存在任务
                if crane2.position >= crane2.task.start_position + crane2.speed:
                    slave_crane, slave_direction = crane2, Direction.STOP
            else:
                # 天车2不存在任务
                slave_crane, slave_direction = crane2, Direction.STOP
    elif crane1.state.lifting_status == LiftingStatus.NOT_LIFTING \
            and crane1.state.load_status == LoadStatus.LOADED:
        # 天车1处于运输状态
        if crane2.state.lifting_status in [LiftingStatus.LIFTING, LiftingStatus.LOWERING]:
            # 天车2起吊或者落吊状态
            if crane1.direction != Direction.LEFT:
                # 天车1不向左运动时，停止天车
                slave_crane, slave_direction = crane1, Direction.STOP
        elif crane2.state.lifting_status == LiftingStatus.NOT_LIFTING \
                and crane2.state.load_status == LoadStatus.LOADED:
            # 天车2处于运输状态
            slave_crane, slave_direction = avoidance_strategy(crane1, crane2)
        else:
            # 天车2未负载
            if crane2.task:
                # 天车2存在任务
                if crane2.position >= crane2.task.start_position - crane2.speed:
                    slave_crane = crane2
                    slave_direction = Direction.STOP if crane1.direction == Direction.LEFT else Direction.RIGHT
            else:
                # 天车2不存在任务
                slave_crane = crane2
                slave_direction = Direction.STOP if crane1.direction == Direction.LEFT else Direction.RIGHT
    else:
        # 天车1未负载
        if crane1.task:
            # 天车1存在任务
            if crane2.state.lifting_status in [LiftingStatus.LIFTING, LiftingStatus.LOWERING]:
                # 天车2起吊或者落吊状态
                if crane1.task.end_position < crane1.task.start_position < crane1.position:
                    # 不做改变
                    pass
                else:
                    slave_crane, slave_direction = crane1, Direction.STOP

            elif crane2.state.lifting_status == LiftingStatus.NOT_LIFTING \
                    and crane2.state.load_status == LoadStatus.LOADED:
                # 天车2处于运输状态
                if crane2.direction == Direction.LEFT \
                        and crane1.position <= crane1.task.start_position - crane1.speed:
                    slave_crane, slave_direction = crane1, Direction.LEFT
            else:
                if crane2.task:
                    # 天车2存在任务
                    slave_crane, slave_direction = avoidance_strategy(crane1, crane2)
                else:
                    # 天车2不存在任务
                    slave_crane = crane2
                    slave_direction = Direction.STOP if crane1.direction == Direction.LEFT else Direction.RIGHT
        else:
            # 天车1不存在任务
            if crane2.task:
                slave_crane = crane1
                slave_direction = Direction.STOP if crane2.direction == Direction.RIGHT else Direction.LEFT
            else:
                # 都不存在任务, 等待算法分配任务
                pass

    return slave_crane, slave_direction


def avoidance_strategy(crane1, crane2):
    avoid_crane = None
    avoid_direction = None
    crane1_target = crane1.task.start_position if crane1.state.load_status == LoadStatus.EMPTY \
        else crane1.task.end_position
    crane2_target = crane2.task.start_position if crane2.state.load_status == LoadStatus.EMPTY \
        else crane2.task.end_position
    if crane1.direction == Direction.LEFT and crane2.direction == Direction.RIGHT:
        # 天车1向左运动，天车2向右运动，不做改变
        pass
    else:
        # 计算两天车距离各自任务终点的距离，距离较大的天车避让
        if abs(crane1_target - crane1.position) > abs(crane2_target - crane2.position):
            avoid_crane, avoid_direction = crane1, crane2.direction
        else:
            avoid_crane, avoid_direction = crane2, crane1.direction

    return avoid_crane, avoid_direction
