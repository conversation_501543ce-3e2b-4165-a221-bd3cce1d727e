"""
API Service for handling external API interactions.

This module provides a centralized service for all external API calls
with proper error handling, configuration management, and logging.
"""

import logging
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Any, Optional

import requests

from config.settings import get_settings

logger = logging.getLogger(__name__)


class APIServiceError(Exception):
    """Raised when API service operations fail."""
    pass


class APIService:
    """Service class for handling external API interactions."""
    
    def __init__(self):
        self.settings = get_settings()
        self.session = requests.Session()
        self.session.timeout = self.settings.api.timeout
    
    def _make_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """
        Make HTTP request with proper error handling.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            **kwargs: Additional request parameters
            
        Returns:
            dict: JSON response data
            
        Raises:
            APIServiceError: If request fails
        """
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"API request failed: {method} {url} - {e}")
            raise APIServiceError(f"API request failed: {e}")
    
    def get_roll_plan_and_restore_slab_list(self) -> tuple:
        """
        Get roll plan and restore slab list from the API.
        
        Returns:
            tuple: (current_plan, restore_slab_list)
        """
        url = f"{self.settings.api.roll_service_url}/api/roll/roll-plan/not-complete-roll-plan"
        response_data = self._make_request("GET", url)
        
        roll_plan = sorted(
            [item for item in response_data if item["status"] < 20],
            key=lambda x: x["planStart"]
        )
        
        current_plan = None
        for roll_group in roll_plan:
            for item in roll_group['rollPlanItems']:
                if item['status'] < 1:
                    current_plan = roll_group
                    break
            if current_plan:
                break
        
        if not current_plan:
            raise APIServiceError("当前没有未完成的入炉计划")
        
        restore_slab_list = [
            item["slabNo"]
            for item in current_plan["rollPlanItems"] 
            if item["needRestorage"]
        ]
        
        return current_plan, restore_slab_list
    
    def get_cut_plan(self, start_time, end_time) -> List[Dict[str, Any]]:
        """
        Get cutting plan from the API.
        
        Args:
            start_time: Plan start time
            end_time: Plan end time
            
        Returns:
            list: Cutting plan data
        """
        url = f"{self.settings.api.steel_service_url}/api/steel/cutting-plan"
        params = {
            "startTime": start_time,
            "endTime": end_time,
            "isPage": False,
        }
        
        response_data = self._make_request("GET", url, params=params)
        cut_plan = sorted(response_data['items'], key=lambda x: x['endTime'])
        
        # 排除其中casterNo为'CC603'的数据
        cut_plan = [item for item in cut_plan if item['casterNo'] != 'CC603']
        
        return cut_plan
    
    def get_stock_slabs(self) -> Dict[str, Any]:
        """
        Get stock slab information from the API.
        
        Returns:
            dict: Stock slab information
        """
        url = f"{self.settings.api.roll_service_url}/api/roll/slab-pile-definition/stock-slabs"
        return self._make_request("GET", url)
    
    def get_crane_info(self) -> Dict[str, Any]:
        """
        Get crane information from the API.
        
        Returns:
            dict: Crane information
        """
        url = f"{self.settings.api.crane_service_url}/api/crane/crane-state"
        crane_number_list = [
            "CRA231", "CRA232", "CRA241", "CRA242", 
            "CRA251", "CRA252", "CRA253", "CRA261", "CRA262"
        ]
        params = {"craneNoList": crane_number_list}
        
        return self._make_request("GET", url, params=params)
    
    def get_restore_plan_by_slab_no(self, slab_no: str) -> Dict[str, Any]:
        """
        Get restore plan by slab number from the API.
        
        Args:
            slab_no: Slab number to query
            
        Returns:
            dict: Restore plan data
        """
        url = f"{self.settings.api.roll_service_url}/api/roll/slab-restorage-plan"
        params = {
            "isPage": False,
            "targetSlabNo": slab_no,
            "statusList": [-1, 0, 1],
        }
        
        headers = {
            "Connection": "keep-alive",
            "Content-Type": "application/json",
        }
        
        # Add authorization header if token is configured
        if self.settings.api.auth_token:
            headers["Authorization"] = f"Bearer {self.settings.api.auth_token}"
        
        return self._make_request("GET", url, params=params, headers=headers)
    
    def get_restore_plan_by_slab_no_list(self, restore_slab_list: List[str]) -> List[Dict[str, Any]]:
        """
        Get restore plans for multiple slab numbers.
        
        Args:
            restore_slab_list: List of slab numbers
            
        Returns:
            list: List of restore plan data
        """
        restore_plan = []
        for slab_no in restore_slab_list:
            restore_plan.append(self.get_restore_plan_by_slab_no(slab_no))
        return restore_plan
    
    def get_all_data(self) -> Dict[str, Any]:
        """
        Get all required data using concurrent requests.
        
        Returns:
            dict: Combined data from all API endpoints
        """
        roll_plan, restore_list = self.get_roll_plan_and_restore_slab_list()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit concurrent requests
            restore_future = executor.submit(self.get_restore_plan_by_slab_no_list, restore_list)
            cut_plan_future = executor.submit(self.get_cut_plan, None, None)  # Will need start/end time
            stock_slabs_future = executor.submit(self.get_stock_slabs)
            crane_info_future = executor.submit(self.get_crane_info)
            
            # Collect results
            data = {
                "roll_plan": roll_plan,
                "restore_plan": restore_future.result(),
                "cut_plan": cut_plan_future.result(),
                "stock_info": stock_slabs_future.result(),
                "crane_info": crane_info_future.result(),
            }
        
        return data
