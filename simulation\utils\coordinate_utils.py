from simulation import config


def real2sim(real_coord):
    offset = config.COORDINATE_OFFSET
    scale = config.COORDINATE_SCALE
    sim_coord = (real_coord - offset) / scale
    return sim_coord


def sim2real(sim_coord):
    offset = config.COORDINATE_OFFSET
    scale = config.COORDINATE_SCALE
    real_coord = sim_coord * scale + offset
    return real_coord


def map_task_to_sim(task):
    task.start_position = real2sim(task.start_position)
    task.end_position = real2sim(task.end_position)
    return task


def map_task_to_real(task):
    task.start_position = sim2real(task.start_position)
    task.end_position = sim2real(task.end_position)
    return task
