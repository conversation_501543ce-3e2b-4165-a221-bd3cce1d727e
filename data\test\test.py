import json
import os

from data import api
from data.lifting_task_generator import Lifting<PERSON><PERSON><PERSON>enerator


def main():
    data = api.get_data()
    lifting_task_generator = LiftingTaskGenerator()

    try:
        os.mkdir('./temp')
    except FileExistsError:
        pass

    with open('./temp/roll_plan.json', "w") as f:
        f.write(json.dumps(data['roll_plan'], indent=4))

    with open('./temp/restore_plan.json', "w") as f:
        f.write(json.dumps(data['restore_plan'], indent=4))

    with open('./temp/cut_plan.json', "w") as f:
        f.write(json.dumps(data['cut_plan'], indent=4))

    with open('./temp/stock_slabs.json', "w") as f:
        f.write(json.dumps(data['stock_info'], indent=4))

    with open('./temp/crane_info.json', "w") as f:
        f.write(json.dumps(data['crane_info'], indent=4))

    lifting_task_generator.create_lifting_tasks(data['roll_plan'], data['cut_plan'], data['restore_plan'])
    with open('./temp/lifting_tasks.csv', "w") as f:
        for task in lifting_task_generator.tasks:
            f.write(f"{task['roll_no']},{task['roll_seq']},{task['slab_no']},{task['virtual_slab_no']},"
                    f"{task['from_pile_no']},{task['from_layer']},{task['to_pile_no']},{task['plan_start_time']},"
                    f"{task['plan_end_time']},{task['span']},{task['property']}\n")


if __name__ == "__main__":
    main()
