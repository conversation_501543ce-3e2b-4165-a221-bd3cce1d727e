from concurrent.futures import ThreadPoolExecutor
import logging

import requests

from config.settings import get_settings
from data import config  # Keep for backward compatibility with start_time/end_time

logger = logging.getLogger(__name__)


def get_roll_plan_and_restore_slab_list():
    """Get roll plan and restore slab list from the API."""
    settings = get_settings()
    url = f"{settings.api.roll_service_url}/api/roll/roll-plan/not-complete-roll-plan"

    try:
        response = requests.get(url, timeout=settings.api.timeout)
        response.raise_for_status()
        res = response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to get roll plan: {e}")
        raise
    roll_plan = sorted(
        [item for item in res if item["status"] < 20],
        key=lambda x: x["planStart"]
    )
    current_plan = None
    for roll_group in roll_plan:
        for item in roll_group['rollPlanItems']:
            if item['status'] < 1:
                current_plan = roll_group
                break
        if current_plan:
            break
    try:
        config.end_time = current_plan['rollPlanItems'][-1]['planInFurnaceTime']
    except TypeError:
        raise ValueError("当前没有未完成的入炉计划")
    restore_slab_list = [
        item["slabNo"]
        for item in current_plan["rollPlanItems"] if item["needRestorage"]
    ]
    return current_plan, restore_slab_list


# 获取切割计划
def get_cut_plan():
    """Get cutting plan from the API."""
    settings = get_settings()
    url = f"{settings.api.steel_service_url}/api/steel/cutting-plan"
    params = {
        "startTime": config.start_time,
        "endTime": config.end_time,
        "isPage": False,
    }

    try:
        response = requests.get(url, params=params, timeout=settings.api.timeout)
        response.raise_for_status()
        res = response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to get cut plan: {e}")
        raise
    cut_plan = sorted(
        res['items'],
        key=lambda x: x['endTime']
    )
    # 排除其中casterNo为'CC603'的数据
    cut_plan = [item for item in cut_plan if item['casterNo'] != 'CC603']
    return cut_plan


# 获取库存板坯信息
def get_stock_slabs():
    """Get stock slab information from the API."""
    settings = get_settings()
    url = f"{settings.api.roll_service_url}/api/roll/slab-pile-definition/stock-slabs"

    try:
        response = requests.get(url, timeout=settings.api.timeout)
        response.raise_for_status()
        stock_info = response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to get stock slabs: {e}")
        raise

    return stock_info


# 获取天车信息
def get_crane_info():
    """Get crane information from the API."""
    settings = get_settings()
    url = f"{settings.api.crane_service_url}/api/crane/crane-state"
    crane_number_list = ["CRA231", "CRA232", "CRA241", "CRA242", "CRA251", "CRA252", "CRA253", "CRA261", "CRA262"]
    params = {
        "craneNoList": crane_number_list,
    }

    try:
        response = requests.get(url, params=params, timeout=settings.api.timeout)
        response.raise_for_status()
        crane_info = response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to get crane info: {e}")
        raise
    # slab_no = [
    #     obj for item in crane_info['items'] if item['objects']
    #     for obj in (item['objects'].split(";") if ";" in item['objects'] else [item['objects']])
    # ]

    return crane_info


def get_restore_plan_by_slab_no(slab_no):
    """Get restore plan by slab number from the API."""
    settings = get_settings()
    url = f"{settings.api.roll_service_url}/api/roll/slab-restorage-plan"
    params = {
        "isPage": False,
        "targetSlabNo": slab_no,
        "statusList": [-1, 0, 1],
    }

    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
    }

    # Add authorization header if token is configured
    if settings.api.auth_token:
        headers["Authorization"] = f"Bearer {settings.api.auth_token}"

    try:
        response = requests.get(url, params=params, headers=headers, timeout=settings.api.timeout)
        response.raise_for_status()
        restore_plan = response.json()
    except requests.RequestException as e:
        logger.error(f"Failed to get restore plan for slab {slab_no}: {e}")
        raise

    return restore_plan


def get_restore_plan_by_slab_no_list(restore_slab_list):
    restore_plan = []
    for slab_no in restore_slab_list:
        restore_plan.append(get_restore_plan_by_slab_no(slab_no))

    return restore_plan


def get_data():
    roll_plan, restore_list = get_roll_plan_and_restore_slab_list()
    with ThreadPoolExecutor(max_workers=5) as executor:
        # 倒垛计划
        restore = executor.submit(get_restore_plan_by_slab_no_list, restore_list)
        # 切割计划
        cut_plan = executor.submit(get_cut_plan)
        # 库存板坯信息
        stock_slabs = executor.submit(get_stock_slabs)
        # 天车信息
        crane_info = executor.submit(get_crane_info)

        data = {
            "roll_plan": roll_plan,
            "restore_plan": restore.result(),
            "cut_plan": cut_plan.result(),
            "stock_info": stock_slabs.result(),
            "crane_info": crane_info.result(),
        }
    return data
