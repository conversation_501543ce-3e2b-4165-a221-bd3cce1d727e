from datetime import datetime
import logging

from config.database import get_oracle_cursor

logger = logging.getLogger(__name__)


def get_x_coordinate(pile_no):
    """
    Get the x coordinate according to the pile_no.

    Args:
        pile_no: The pile number to look up

    Returns:
        float or None: The x coordinate if found, None otherwise
    """
    try:
        with get_oracle_cursor() as cursor:
            sql = "SELECT X FROM SLAB_PILE_DEFINITION WHERE PILE_NO = :pile_no"
            cursor.execute(sql, pile_no=pile_no)
            query_result = cursor.fetchone()

            if query_result is None:
                logger.warning(f"No coordinate found for pile_no: {pile_no}")
                return None
            else:
                return query_result[0]
    except Exception as e:
        logger.error(f"Failed to get x coordinate for pile_no {pile_no}: {e}")
        raise


def instance_to_dict(instance):
    """
    Convert an object instance to a dictionary with proper datetime formatting.

    Args:
        instance: The object instance to convert

    Returns:
        dict: Dictionary representation of the instance
    """
    result_dict = {}
    for attribute_name in vars(instance):
        attribute_value = getattr(instance, attribute_name)
        if isinstance(attribute_value, datetime):
            result_dict[attribute_name] = attribute_value.strftime('%Y-%m-%d %H:%M:%S')
        else:
            result_dict[attribute_name] = attribute_value
    return result_dict