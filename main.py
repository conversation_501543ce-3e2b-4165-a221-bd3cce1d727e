import json
import traceback

import simulation.config
from common import utils
from common.task import Task
from data import api
from data.lifting_task_generator import LiftingTaskGenerator
from simulation.sim_model import SimulationModel
from simulation.utils import coordinate_utils
from task_allocator import allocator

# from simulation.src.env import Env
# from datetime import datetime


def exception_handler(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception:
            return str(traceback.format_exc())

    return wrapper


@exception_handler
def run():
    data = api.get_data()
    allocator_ = allocator.TaskAllocator()
    simulation_model = SimulationModel()
    # env = Env(1500, 850)
    lifting_task_generator = LiftingTaskGenerator()
    lifting_task_generator.create_lifting_tasks(data["roll_plan"], data["cut_plan"], data["restore_plan"])
    tasks = lifting_task_generator.tasks

    # with open('./temp/tasks.csv', 'w') as f:
    #     for task in tasks:
    #         f.write(task['roll_no'] + ',' + task['roll_no'] + ',' + task['slab_no'] + ','
    #                 + task['virtual_slab_no'] + ',' + task['from_pile_no'] + ',' + task['to_pile_no'] + ','
    #                 + str(task['plan_start_time']) + ',' + str(task['plan_end_time']) + ','
    #                 + task['span'] + ',' + str(task['property']) + '\n')

    simu_tasks = []
    for i, task in enumerate(tasks):
        slab_no = task["slab_no"] if task["slab_no"] else task["virtual_slab_no"]
        temp = Task(
            item_id=i,
            start_position=utils.get_x_coordinate(task["from_pile_no"]),
            end_position=utils.get_x_coordinate(task["to_pile_no"]),
            from_pile_no=task["from_pile_no"],
            to_pile_no=task["to_pile_no"],
            start_time=task["plan_start_time"],
            roll_no=task["roll_no"],
            end_time=task["plan_end_time"],
            slab_no=slab_no,
            span=task["span"],
            roll_seq=task["roll_seq"],
            priority=task["property"],
        )
        simu_task = coordinate_utils.map_task_to_sim(temp)
        res = allocator_.assign_task(simu_task)
        simu_tasks.append(res)
    simulation_model.tasks = simu_tasks
    try:
        simulation.config.run_time = simu_tasks[0].start_time
    except IndexError:
        raise ValueError("当前轧程没有可执行的任务！")

    while True:
        # env.handle_event()
        simulation_model.execute()
        simulation_model.run_simulation()
        # env.show(simulation_model.get_all_cranes(), simulation_model.get_all_current_tasks())
        if simulation_model.is_all_tasks_finished():
            result = simulation_model.get_all_results()
            result = [utils.instance_to_dict(obj) for obj in result]
            break
    return result


def main():
    result = {"error_message": "", "items": []}
    if isinstance(run(), str):
        result["error_message"] = run()
    else:
        result["items"] = run()

    return json.dumps(result)


if __name__ == "__main__":
    s = main()
    with open("./temp/result.json", "w") as f:
        f.write(s)
