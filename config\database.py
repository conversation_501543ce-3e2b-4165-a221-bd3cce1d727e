"""
Database configuration and connection management.

This module provides database connection utilities with proper configuration
management and connection pooling support.
"""

import logging
from typing import Optional, Dict, Any
from contextlib import contextmanager

try:
    import oracledb
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    oracledb = None

from .settings import get_settings

logger = logging.getLogger(__name__)


class DatabaseConnectionError(Exception):
    """Raised when database connection fails."""
    pass


class DatabaseManager:
    """Manages database connections with proper configuration."""
    
    def __init__(self):
        self.settings = get_settings()
        self._oracle_pool: Optional[Any] = None
    
    def get_oracle_connection_params(self) -> Dict[str, Any]:
        """Get Oracle connection parameters from configuration."""
        if not self.settings.database.oracle_dsn:
            raise DatabaseConnectionError("Oracle DSN not configured")
        
        return {
            'dsn': self.settings.database.oracle_dsn,
            'encoding': 'UTF-8'
        }
    
    def get_mysql_connection_params(self) -> Dict[str, Any]:
        """Get MySQL connection parameters from configuration."""
        return {
            'user': self.settings.database.mysql_user,
            'password': self.settings.database.mysql_password,
            'host': self.settings.database.mysql_host,
            'port': self.settings.database.mysql_port,
            'database': self.settings.database.mysql_database,
        }
    
    @contextmanager
    def get_oracle_connection(self):
        """Get Oracle database connection with proper error handling."""
        if not ORACLE_AVAILABLE:
            raise DatabaseConnectionError("oracledb package not available")
        
        connection = None
        try:
            params = self.get_oracle_connection_params()
            connection = oracledb.connect(**params)
            logger.debug("Oracle connection established")
            yield connection
        except Exception as e:
            logger.error(f"Failed to connect to Oracle database: {e}")
            raise DatabaseConnectionError(f"Oracle connection failed: {e}")
        finally:
            if connection:
                try:
                    connection.close()
                    logger.debug("Oracle connection closed")
                except Exception as e:
                    logger.warning(f"Error closing Oracle connection: {e}")
    
    @contextmanager
    def get_oracle_cursor(self):
        """Get Oracle cursor with automatic connection management."""
        with self.get_oracle_connection() as connection:
            cursor = None
            try:
                cursor = connection.cursor()
                yield cursor
            finally:
                if cursor:
                    try:
                        cursor.close()
                    except Exception as e:
                        logger.warning(f"Error closing Oracle cursor: {e}")


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


# Convenience functions for backward compatibility
def get_oracle_connection():
    """Get Oracle connection (backward compatibility)."""
    return get_database_manager().get_oracle_connection()


def get_oracle_cursor():
    """Get Oracle cursor (backward compatibility)."""
    return get_database_manager().get_oracle_cursor()
