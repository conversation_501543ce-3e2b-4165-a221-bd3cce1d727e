# Core dependencies for crane scheduling system

# Environment variable management
python-dotenv>=1.0.0

# Database connectivity
oracledb>=1.4.0

# HTTP requests for API calls
requests>=2.31.0

# Development dependencies (optional)
# Uncomment these for development environment
# pytest>=7.4.0
# pytest-cov>=4.1.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.5.0

# Note: Add specific version pins for production deployment
# Example:
# python-dotenv==1.0.0
# oracledb==1.4.2
# requests==2.31.0
