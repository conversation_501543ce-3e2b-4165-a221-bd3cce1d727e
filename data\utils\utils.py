import oracledb

import config


# 计算两个垛位之间的距离
def compute_distance_between_piles(pile1, pile2):
    conn = oracledb.connect(config.ORACLE_DSN)
    cursor = conn.cursor()
    sql = """
    SELECT X FROM SLAB_PILE_DEFINITION WHERE PILE_NO = :pile_no1
    UNION ALL
    SELECT X FROM SLAB_PILE_DEFINITION WHERE PILE_NO = :pile_no2
    """
    cursor.execute(sql, [pile1, pile2])
    result = cursor.fetchall()
    cursor.close()
    conn.close()
    distance = abs(result[0][0] - result[1][0])
    return distance
