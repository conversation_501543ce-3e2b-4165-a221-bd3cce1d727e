from datetime import datetime

import oracledb

import config


def get_x_coordinate(pile_no):
    """
    Get the x coordinate according to the pile_no
    """
    conn = oracledb.connect(config.ORACLE_DSN)
    cursor = conn.cursor()
    sql = "select X from SLAB_PILE_DEFINITION where PILE_NO = :pile_no"
    cursor.execute(sql, pile_no=pile_no)
    result_ = cursor.fetchone()
    cursor.close()
    conn.close()
    if result_ is None:
        return None
    else:
        return result_[0]


def instance_to_dict(instance):
    result = {}
    for attr in vars(instance):
        value = getattr(instance, attr)
        if isinstance(value, datetime):
            result[attr] = value.strftime('%Y-%m-%d %H:%M:%S')
        else:
            result[attr] = value
    return result