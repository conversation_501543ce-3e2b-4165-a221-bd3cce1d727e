# Legacy configuration file - DEPRECATED
# This file is being refactored. Please use the new configuration system in config/ directory.
# This file will be removed in a future version.

import warnings
from config.settings import get_settings

warnings.warn(
    "Direct import from config.py is deprecated. Use 'from config.settings import get_settings' instead.",
    DeprecationWarning,
    stacklevel=2
)

# Backward compatibility - delegate to new configuration system
_settings = get_settings()

ORACLE_DSN = _settings.database.oracle_dsn
CRANE_SPEED = _settings.crane.speed
CRANE_WORKING_INTERVALS = _settings.crane.working_intervals
