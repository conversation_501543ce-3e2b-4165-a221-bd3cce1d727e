"""
Crane Service for handling crane-related operations.

This module provides services for crane management, task allocation,
and coordinate calculations.
"""

import logging
from typing import Dict, List, Optional

from config.settings import get_settings
from config.database import get_oracle_cursor

logger = logging.getLogger(__name__)


class CraneServiceError(Exception):
    """Raised when crane service operations fail."""
    pass


class CraneService:
    """Service class for crane-related operations."""
    
    def __init__(self):
        self.settings = get_settings()
    
    def get_crane_name_by_number(self, crane_number: str) -> Optional[str]:
        """
        Get crane display name by crane number.
        
        Args:
            crane_number: Crane number (e.g., 'CRA231')
            
        Returns:
            str or None: Crane display name (e.g., '46#') or None if not found
        """
        return self.settings.simulation.crane_name_mapping.get(crane_number)
    
    def get_crane_working_intervals(self, span: str) -> Dict[str, List[int]]:
        """
        Get working intervals for cranes in a specific span.
        
        Args:
            span: Span identifier (e.g., 'SPA23')
            
        Returns:
            dict: Mapping of crane numbers to their working intervals
        """
        return self.settings.crane.working_intervals.get(span, {})
    
    def get_crane_speed(self) -> int:
        """
        Get crane speed configuration.
        
        Returns:
            int: Crane speed in mm/s
        """
        return self.settings.crane.speed
    
    def get_simulation_crane_speed(self) -> float:
        """
        Get simulation crane speed (scaled).
        
        Returns:
            float: Scaled crane speed for simulation
        """
        return self.get_crane_speed() / self.settings.simulation.coordinate_scale
    
    def get_pile_x_coordinate(self, pile_number: str) -> Optional[float]:
        """
        Get the x coordinate for a pile number from the database.
        
        Args:
            pile_number: The pile number to look up
            
        Returns:
            float or None: The x coordinate if found, None otherwise
        """
        try:
            with get_oracle_cursor() as cursor:
                sql = "SELECT X FROM SLAB_PILE_DEFINITION WHERE PILE_NO = :pile_no"
                cursor.execute(sql, pile_no=pile_number)
                query_result = cursor.fetchone()
                
                if query_result is None:
                    logger.warning(f"No coordinate found for pile_no: {pile_number}")
                    return None
                else:
                    return query_result[0]
        except Exception as e:
            logger.error(f"Failed to get x coordinate for pile_no {pile_number}: {e}")
            raise CraneServiceError(f"Database query failed: {e}")
    
    def convert_real_to_simulation_coordinate(self, real_coordinate: float) -> float:
        """
        Convert real coordinate to simulation coordinate.
        
        Args:
            real_coordinate: Real world coordinate
            
        Returns:
            float: Simulation coordinate
        """
        offset = self.settings.simulation.coordinate_offset
        scale = self.settings.simulation.coordinate_scale
        return (real_coordinate - offset) / scale
    
    def convert_simulation_to_real_coordinate(self, sim_coordinate: float) -> float:
        """
        Convert simulation coordinate to real coordinate.
        
        Args:
            sim_coordinate: Simulation coordinate
            
        Returns:
            float: Real world coordinate
        """
        offset = self.settings.simulation.coordinate_offset
        scale = self.settings.simulation.coordinate_scale
        return sim_coordinate * scale + offset
    
    def get_safe_distance(self) -> Dict[str, float]:
        """
        Get safe distance configurations.
        
        Returns:
            dict: Safe distances for real and simulation environments
        """
        real_distance = self.settings.simulation.real_safe_distance
        sim_distance = real_distance / self.settings.simulation.coordinate_scale
        
        return {
            "real": real_distance,
            "simulation": sim_distance
        }
    
    def validate_crane_number(self, crane_number: str) -> bool:
        """
        Validate if a crane number is known in the system.
        
        Args:
            crane_number: Crane number to validate
            
        Returns:
            bool: True if crane number is valid, False otherwise
        """
        return crane_number in self.settings.simulation.crane_name_mapping
    
    def get_all_crane_numbers(self) -> List[str]:
        """
        Get all known crane numbers in the system.
        
        Returns:
            list: List of all crane numbers
        """
        return list(self.settings.simulation.crane_name_mapping.keys())
