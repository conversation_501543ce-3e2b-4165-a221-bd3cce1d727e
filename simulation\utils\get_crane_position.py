import requests


def get_crane_position(crane_no):
    url = "http://10.220.23.28:44360/api/crane/crane-state"
    params = {
        "craneNo": crane_no,
    }
    crane_info = requests.get(url, params=params).json()

    if crane_info['totalCount']:
        position = crane_info['items'][0]['x']
        return position
    else:
        raise IndexError(f"Cannot find crane {crane_no} in the database")
