from copy import deepcopy

from common.base import SlabYardModel
from task_allocator.utils import utils


class TaskAllocator(SlabYardModel):
    def __init__(self):
        super().__init__()

    def assign_task(self, unassigned_task):
        assigned_task = deepcopy(unassigned_task)
        cranes = self.spans[unassigned_task.span]
        task_interval = [
            min(unassigned_task.start_position, unassigned_task.end_position),
            max(unassigned_task.start_position, unassigned_task.end_position)
        ]
        if unassigned_task.span != 'SPA25':
            overlap_working_intervals = utils.find_intersection(
                cranes[0].working_intervals, cranes[1].working_intervals)
            if utils.is_interval_inside(task_interval, overlap_working_intervals):
                if cranes[0].task_num <= cranes[1].task_num:
                    cranes[0].task_num += 1
                    assigned_task.crane_no = cranes[0].crane_no
                else:
                    cranes[1].task_num += 1
                    assigned_task.crane_no = cranes[1].crane_no
            else:
                coincidence_degree1 = utils.get_coincidence_degree(
                    task_interval, cranes[0].working_intervals)
                coincidence_degree2 = utils.get_coincidence_degree(
                    task_interval, cranes[1].working_intervals)
                if coincidence_degree1 >= coincidence_degree2:
                    cranes[0].task_num += 1
                    assigned_task.crane_no = cranes[0].crane_no
                else:
                    cranes[1].task_num += 1
                    assigned_task.crane_no = cranes[1].crane_no
        else:
            overlap_working_intervals_1 = utils.find_intersection(
                cranes[0].working_intervals, cranes[1].working_intervals)
            overlap_working_intervals_2 = utils.find_intersection(
                cranes[1].working_intervals, cranes[2].working_intervals)
            if utils.is_interval_inside(task_interval, overlap_working_intervals_1):
                if cranes[0].task_num <= cranes[1].task_num:
                    cranes[0].task_num += 1
                    assigned_task.crane_no = cranes[0].crane_no
                else:
                    cranes[1].task_num += 1
                    assigned_task.crane_no = cranes[1].crane_no
            elif utils.is_interval_inside(task_interval, overlap_working_intervals_2):
                if cranes[1].task_num <= cranes[2].task_num:
                    cranes[1].task_num += 1
                    assigned_task.crane_no = cranes[1].crane_no
                else:
                    cranes[2].task_num += 1
                    assigned_task.crane_no = cranes[2].crane_no
            else:
                coincidence_degree1 = utils.get_coincidence_degree(task_interval, cranes[0].working_intervals)
                coincidence_degree2 = utils.get_coincidence_degree(task_interval, cranes[1].working_intervals)
                coincidence_degree3 = utils.get_coincidence_degree(task_interval, cranes[2].working_intervals)
                if coincidence_degree1 >= coincidence_degree2 and coincidence_degree1 >= coincidence_degree3:
                    cranes[0].task_num += 1
                    assigned_task.crane_no = cranes[0].crane_no
                elif coincidence_degree2 >= coincidence_degree1 and coincidence_degree2 >= coincidence_degree3:
                    cranes[1].task_num += 1
                    assigned_task.crane_no = cranes[1].crane_no
                else:
                    cranes[2].task_num += 1
                    assigned_task.crane_no = cranes[2].crane_no

        return assigned_task
